<template>
  <a-drawer
    :title="isAdd ? '新增代理商' : '修改代理商'"
    :width="800"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
  >
    <a-form
      ref="infoFormModel"
      :model="saveObject"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-row>
        <a-col :span="24" v-if="!isAdd">
          <a-form-item label="代理商号" name="agentNo">
            <a-input v-model:value="saveObject.agentNo" placeholder="系统自动生成" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="代理商名称" name="agentName" :rules="[{ required: true, message: '请输入代理商名称' }]">
            <a-input v-model:value="saveObject.agentName" placeholder="请输入代理商名称" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="登录用户名" name="loginUsername" :rules="[{ required: true, message: '请输入登录用户名' }]">
            <a-input v-model:value="saveObject.loginUsername" placeholder="请输入登录用户名" :disabled="!isAdd" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="代理商简称" name="agentShortName" :rules="[{ required: true, message: '请输入代理商简称' }]">
            <a-input v-model:value="saveObject.agentShortName" placeholder="请输入代理商简称" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="上级代理商" name="parentAgentNo">
            <a-select v-model:value="saveObject.parentAgentNo" placeholder="请选择上级代理商" allow-clear>
              <a-select-option v-for="item in agentList" :key="item.agentNo" :value="item.agentNo">
                {{ item.agentName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="联系人姓名" name="contactName" :rules="[{ required: true, message: '请输入联系人姓名' }]">
            <a-input v-model:value="saveObject.contactName" placeholder="请输入联系人姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="联系人手机" name="contactTel" :rules="[{ required: true, pattern: /^1\d{10}$/, message: '请输入正确的手机号' }]">
            <a-input v-model:value="saveObject.contactTel" placeholder="请输入联系人手机号" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="联系人邮箱" name="contactEmail">
            <a-input v-model:value="saveObject.contactEmail" placeholder="请输入联系人邮箱" />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="isAdd">
          <a-form-item label="登录密码" name="loginPassword" :rules="[{ required: true, message: '请输入登录密码' }]">
            <a-input-password v-model:value="saveObject.loginPassword" placeholder="请输入登录密码" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="分润比例" name="profitRate">
            <a-input-number
              v-model:value="saveObject.profitRate"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="4"
              placeholder="请输入分润比例"
              style="width: 100%"
            />
            <div class="ant-form-explain">分润比例范围：0-1，例如：0.05 表示 5%</div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="允许发展下级代理商" name="canDevelopAgent">
            <a-radio-group v-model:value="saveObject.canDevelopAgent">
              <a-radio :value="1">是</a-radio>
              <a-radio :value="0">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="允许发展下级商户" name="canDevelopMch">
            <a-radio-group v-model:value="saveObject.canDevelopMch">
              <a-radio :value="1">是</a-radio>
              <a-radio :value="0">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="状态" name="state">
            <a-radio-group v-model:value="saveObject.state">
              <a-radio :value="1">正常</a-radio>
              <a-radio :value="0">停用</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="saveObject.remark"
              placeholder="请输入备注信息"
              :auto-size="{ minRows: 2, maxRows: 6 }"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <div class="drawer-btn-center">
      <a-button :style="{ marginRight: '8px' }" @click="onClose">取消</a-button>
      <a-button type="primary" @click="handleOkFunc" :loading="btnLoading">确认</a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_AGENT_LIST, req } from '@/api/manage'
import { reactive, ref, toRefs, getCurrentInstance } from 'vue'
const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const infoFormModel = ref()

const vdata: any = reactive({
  btnLoading: false,
  isAdd: true, // 新增 or 修改页面标志
  saveObject: {}, // 数据对象
  recordId: null, // 更新对象ID
  open: false, // 是否显示弹层/抽屉
  agentList: [], // 代理商下拉列表
})

const { btnLoading, isAdd, saveObject, recordId, open, agentList } = toRefs(vdata)

function show(recordId) {
  // 弹层打开事件
  vdata.isAdd = !recordId

  vdata.saveObject = { 
    state: 1, 
    canDevelopAgent: 1, 
    canDevelopMch: 1 
  } // 数据清空并设置默认值
  if (infoFormModel.value) {
    infoFormModel.value.resetFields()
  }
  
  // 加载上级代理商下拉列表（只加载当前代理商的上级，避免循环引用）
  // 暂时设置为空数组，实际应该从后端获取可选的上级代理商列表
  vdata.agentList = []
  
  if (!vdata.isAdd) {
    // 修改信息 延迟展示弹层
    vdata.recordId = recordId

    req.getById(API_URL_AGENT_LIST, recordId).then((res) => {
      vdata.saveObject = res
    })
    vdata.open = true
  } else {
    vdata.open = true // 立马展示弹层信息
  }
}

function handleOkFunc() {
  // 点击【确认】按钮事件
  infoFormModel.value.validate().then((valid) => {
    if (valid) {
      // 验证通过
      // 请求接口
      if (vdata.isAdd) {
        vdata.btnLoading = true
        req
          .add(API_URL_AGENT_LIST, vdata.saveObject)
          .then((res) => {
            $infoBox.message.success('新增成功')
            vdata.open = false
            props.callbackFunc() // 刷新列表
            vdata.btnLoading = false
          })
          .catch((res) => {
            vdata.btnLoading = false
          })
      } else {
        req
          .updateById(API_URL_AGENT_LIST, vdata.recordId, vdata.saveObject)
          .then((res) => {
            $infoBox.message.success('修改成功')
            vdata.open = false
            props.callbackFunc() // 刷新列表
            vdata.btnLoading = false
          })
          .catch((res) => {
            vdata.btnLoading = false
          })
      }
    }
  })
}

function onClose() {
  vdata.open = false
}

defineExpose({
  show
})
</script>

<style scoped>
.drawer-btn-center {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
