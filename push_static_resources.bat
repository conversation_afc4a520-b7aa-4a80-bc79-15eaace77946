@echo off
chcp 65001 > nul 2>&1  :: 设置编码为UTF-8以解决中文显示问题
setlocal enabledelayedexpansion

:: 定义源目录和目标目录的映射关系
set "source1=unipay-web-ui\unipay-ui-agent\dist"
set "dest1=sys-agent\src\main\resources\static"

set "source2=unipay-web-ui\unipay-ui-manager\dist"
set "dest2=sys-manager\src\main\resources\static"

set "source3=unipay-web-ui\unipay-ui-merchant\dist"
set "dest3=sys-merchant\src\main\resources\static"

set "source4=unipay-web-ui\unipay-ui-payment\dist"
set "dest4=sys-payment\src\main\resources\static"

:: 循环处理所有目录对
for /l %%i in (1,1,4) do (
    :: 获取当前源和目标路径
    set "source=!source%%i!"
    set "dest=!dest%%i!"
    
    echo 处理 !source! 到 !dest!...
    
    :: 检查目标目录是否存在，不存在则创建
    if not exist "!dest!" (
        echo 目标目录 !dest! 不存在，正在创建...
        mkdir "!dest!"
        if errorlevel 1 (
            echo 创建目录 !dest! 失败，请检查权限
            exit /b 1
        )
    )
    
    :: 执行拷贝操作，/E复制所有子目录(包括空目录)，/H复制隐藏和系统文件，/Y覆盖不提示
    echo 正在拷贝文件及子目录...
    xcopy "!source!\*" "!dest!" /E /H /Y
    if errorlevel 2 (
        echo 拷贝 !source! 到 !dest! 失败
        exit /b 1
    )
)

echo 所有文件及子目录拷贝完成
endlocal