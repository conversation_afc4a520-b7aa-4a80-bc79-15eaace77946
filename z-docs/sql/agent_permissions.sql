-- 代理商系统完整权限配置
-- 执行此脚本来为代理商系统添加完整的菜单和权限

-- ========================================
-- 1. 代理商系统基础权限（AGENT系统类型）
-- ========================================

-- 通用菜单
INSERT INTO t_sys_entitlement VALUES('ENT_COMMONS', '系统通用菜单', 'no-icon', '', 'RouteView', 'MO', 0, 1, 'ROOT', '-1', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_USERINFO', '个人中心', 'no-icon', '/current/userinfo', 'CurrentUserInfo', 'MO', 0, 1, 'ENT_COMMONS', '-1', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 主页
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN', '主页', 'home', '/main', 'MainPage', 'ML', 0, 1, 'ROOT', '1', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_PAY_AMOUNT_WEEK', '主页周支付统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_NUMBER_COUNT', '主页数量总统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_PAY_COUNT', '主页交易统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_PAY_TYPE_COUNT', '主页交易方式统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_USER_INFO', '主页用户信息', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_C_MAIN_AGENT_MCH_COUNT', '主页代理商商户数量统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 分润管理菜单和权限
INSERT INTO t_sys_entitlement VALUES('ENT_PROFIT', '分润管理', 'money-collect', '', '', 'ML', 0, 1, 'ROOT', '60', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PROFIT_RECORD', '分润记录', 'unordered-list', '/profitRecord', 'ProfitRecordListPage', 'ML', 0, 1, 'ENT_PROFIT', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PROFIT_RECORD_LIST', '页面：分润记录列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PROFIT_RECORD', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PROFIT_RECORD_VIEW', '按钮：分润记录详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PROFIT_RECORD', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PROFIT_RECORD_STATISTICS', '按钮：分润统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PROFIT_RECORD', '20', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 商户管理
INSERT INTO t_sys_entitlement VALUES('ENT_MCH', '商户管理', 'shop', '', 'RouteView', 'ML', 0, 1, 'ROOT', '30', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_INFO', '商户列表', 'profile', '/mch', 'MchListPage', 'ML', 0, 1, 'ENT_MCH', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_LIST', '页面：商户列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_INFO_ADD', '按钮：新增', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_INFO_EDIT', '按钮：编辑', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_INFO_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 商户应用管理
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP', '应用管理', 'appstore', '/mchApp', 'MchAppListPage', 'ML', 0, 1, 'ENT_MCH', '20', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP_LIST', '页面：应用列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_APP', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP_ADD', '按钮：新增应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_APP', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP_EDIT', '按钮：编辑应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_APP', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP_VIEW', '按钮：应用详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_APP', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_APP_DEL', '按钮：删除应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_APP', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 代理商管理
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT', '代理商管理', 'team', '', 'RouteView', 'ML', 0, 1, 'ROOT', '20', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_INFO', '下级代理商', 'user', '/agent', 'AgentListPage', 'ML', 0, 1, 'ENT_AGENT', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_LIST', '页面：代理商列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_INFO_ADD', '按钮：新增', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_INFO_EDIT', '按钮：编辑', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_INFO_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_AGENT_INFO_DELETE', '按钮：删除', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 订单管理
INSERT INTO t_sys_entitlement VALUES('ENT_ORDER', '订单管理', 'account-book', '', 'RouteView', 'ML', 0, 1, 'ROOT', '40', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PAY_ORDER', '支付订单', 'file-text', '/payOrder', 'PayOrderListPage', 'ML', 0, 1, 'ENT_ORDER', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_ORDER_LIST', '页面：订单列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PAY_ORDER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PAY_ORDER_REFUND', '按钮：发起退款', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

INSERT INTO t_sys_entitlement VALUES('ENT_REFUND_ORDER', '退款订单', 'file-text', '/refundOrder', 'RefundOrderListPage', 'ML', 0, 1, 'ENT_ORDER', '20', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_REFUND_LIST', '页面：退款订单列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_REFUND_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_REFUND_ORDER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_REFUND_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

INSERT INTO t_sys_entitlement VALUES('ENT_TRANSFER_ORDER', '转账订单', 'property-safety', '/transferOrder', 'TransferOrderListPage', 'ML', 0, 1, 'ENT_ORDER', '25', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_TRANSFER_ORDER_LIST', '页面：转账订单列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_TRANSFER_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_TRANSFER_ORDER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_TRANSFER_ORDER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

INSERT INTO t_sys_entitlement VALUES('ENT_MCH_NOTIFY', '商户通知', 'notification', '/mchNotify', 'MchNotifyListPage', 'ML', 0, 1, 'ENT_ORDER', '30', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_NOTIFY_LIST', '页面：商户通知列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_NOTIFY', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_NOTIFY_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_NOTIFY', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_MCH_NOTIFY_RESEND', '按钮：重发通知', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_NOTIFY', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 支付配置
INSERT INTO t_sys_entitlement VALUES('ENT_PC', '支付配置', 'setting', '', 'RouteView', 'ML', 0, 1, 'ROOT', '50', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PC_WAY', '支付方式', 'credit-card', '/payways', 'PayWayPage', 'ML', 0, 1, 'ENT_PC', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PC_WAY_LIST', '页面：支付方式列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PC_WAY', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_PC_WAY_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PC_WAY', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- 系统管理
INSERT INTO t_sys_entitlement VALUES('ENT_SYS_CONFIG', '系统管理', 'setting', '', 'RouteView', 'ML', 0, 1, 'ROOT', '200', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR', '用户角色管理', 'team', '', 'RouteView', 'ML', 0, 1, 'ENT_SYS_CONFIG', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_USER', '操作员管理', 'contacts', '/users', 'SysUserPage', 'ML', 0, 1, 'ENT_UR', '10', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_USER_LIST', '页面：操作员列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_USER_ADD', '按钮：新增操作员', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_USER_EDIT', '按钮：编辑操作员', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_USER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

INSERT INTO t_sys_entitlement VALUES('ENT_UR_ROLE', '角色管理', 'solution', '/roles', 'SysRolePage', 'ML', 0, 1, 'ENT_UR', '20', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_ROLE_LIST', '页面：角色列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_ROLE_ADD', '按钮：新增角色', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_ROLE_EDIT', '按钮：编辑角色', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);
INSERT INTO t_sys_entitlement VALUES('ENT_UR_ROLE_DIST', '按钮：分配权限', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW())
ON DUPLICATE KEY UPDATE ent_name=VALUES(ent_name);

-- ========================================
-- 2. 创建代理商系统默认角色
-- ========================================

-- 创建代理商管理员角色
INSERT INTO t_sys_role VALUES ('ROLE_AGENT_ADMIN', '代理商管理员', 'AGENT', '0', NOW())
ON DUPLICATE KEY UPDATE role_name=VALUES(role_name);

-- ========================================
-- 3. 为代理商管理员角色分配权限
-- ========================================

-- 基础权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_COMMONS') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_USERINFO') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 主页权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_PAY_AMOUNT_WEEK') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_NUMBER_COUNT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_PAY_COUNT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_PAY_TYPE_COUNT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_USER_INFO') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_C_MAIN_AGENT_MCH_COUNT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 分润管理权限分配
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PROFIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PROFIT_RECORD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PROFIT_RECORD_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PROFIT_RECORD_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PROFIT_RECORD_STATISTICS') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 商户管理权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_INFO') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_INFO_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_INFO_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_INFO_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 商户应用管理权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_APP_DEL') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 代理商管理权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_INFO') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_INFO_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_INFO_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_INFO_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_AGENT_INFO_DELETE') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 订单管理权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_ORDER') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PAY_ORDER') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_ORDER_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PAY_ORDER_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PAY_ORDER_REFUND') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_REFUND_ORDER') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_REFUND_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_REFUND_ORDER_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_TRANSFER_ORDER') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_TRANSFER_ORDER_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_TRANSFER_ORDER_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_NOTIFY') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_NOTIFY_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_NOTIFY_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_MCH_NOTIFY_RESEND') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 支付配置权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PC') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PC_WAY') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PC_WAY_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_PC_WAY_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- 系统管理权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_SYS_CONFIG') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_USER') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_USER_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_USER_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_USER_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_USER_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_ROLE') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_ROLE_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_ROLE_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_ROLE_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_AGENT_ADMIN', 'ENT_UR_ROLE_DIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id);

-- ========================================
-- 4. 验证和查询
-- ========================================

-- 查询代理商系统权限
SELECT '=== 代理商系统权限列表 ===' as info;
SELECT ent_id, ent_name, ent_type, pid, ent_sort
FROM t_sys_entitlement
WHERE sys_type = 'AGENT'
ORDER BY pid, ent_sort;

-- 查询代理商管理员角色权限
SELECT '=== 代理商管理员角色权限 ===' as info;
SELECT COUNT(*) as permission_count
FROM t_sys_role_ent_rela
WHERE role_id = 'ROLE_AGENT_ADMIN';

COMMIT;
