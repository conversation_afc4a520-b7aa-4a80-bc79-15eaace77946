#!/bin/bash
set -e  # 出错时退出
set -u  # 使用未定义变量时报错

# 切换到脚本所在目录（避免相对路径出错）
cd "$(dirname "$0")"

# 定义源目录和目标目录的数组
sources=(
  "unipay-web-ui/unipay-ui-agent/dist"
  "unipay-web-ui/unipay-ui-manager/dist"
  "unipay-web-ui/unipay-ui-merchant/dist"
  "unipay-web-ui/unipay-ui-cashier/dist"
)

dests=(
  "sys-agent/src/main/resources/static"
  "sys-manager/src/main/resources/static"
  "sys-merchant/src/main/resources/static"
  "sys-payment/src/main/resources/static"
)

# 遍历数组
for i in "${!sources[@]}"; do
  src="${sources[$i]}"
  dst="${dests[$i]}"

  echo "正在处理 $src → $dst ..."

  # 检查源目录是否存在
  if [ ! -d "$src" ]; then
    echo "❌ 源目录 $src 不存在，跳过"
    continue
  fi

  # 检查目标目录是否存在，不存在则创建
  if [ ! -d "$dst" ]; then
    echo "目标目录 $dst 不存在，正在创建..."
    mkdir -p "$dst" || { echo "❌ 创建目录 $dst 失败"; exit 1; }
  fi

  # 拷贝文件（-a 保留属性，-v 显示过程，--delete 可选：删除目标中多余文件）
  echo "拷贝文件中..."
  rsync -av --delete "$src/" "$dst/" || { echo "❌ 拷贝 $src 到 $dst 失败"; exit 1; }
done

echo "✅ 所有文件及子目录拷贝完成"
