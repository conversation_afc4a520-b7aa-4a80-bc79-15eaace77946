#!/bin/bash

# UniPay UI构建脚本
# 支持编译单个模块或所有模块

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
MODULE_NAME=""
BUILD_ALL=false

# 支持的UI模块
declare -A UI_MODULES
UI_MODULES["agent"]="unipay-ui-agent"
UI_MODULES["manager"]="unipay-ui-manager"
UI_MODULES["merchant"]="unipay-ui-merchant"
UI_MODULES["cashier"]="unipay-ui-cashier"

# 所有模块列表
ALL_MODULES=("agent" "manager" "merchant" "cashier")

# 打印使用说明
usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -m, --module MODULE     指定要构建的UI模块 (agent, manager, merchant, cashier)"
    echo "  -a, --all              构建所有UI模块"
    echo "  -l, --list             列出所有支持的UI模块"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -m agent            # 构建代理商UI模块"
    echo "  $0 --module manager    # 构建运营平台UI模块"
    echo "  $0 -a                  # 构建所有UI模块"
    echo "  $0 --all               # 构建所有UI模块"
    echo "  $0 -l                  # 列出所有支持的UI模块"
    exit 1
}

# 列出所有支持的模块
list_modules() {
    echo -e "${BLUE}支持的UI模块:${NC}"
    for module in "${ALL_MODULES[@]}"; do
        echo "  $module (${UI_MODULES[$module]})"
    done
}

# 构建单个模块
build_module() {
    local module_key=$1
    local module_dir=${UI_MODULES[$module_key]}
    
    if [ -z "$module_dir" ]; then
        echo -e "${RED}错误: 不支持的模块 '$module_key'${NC}"
        list_modules
        exit 1
    fi
    
    if [ ! -d "unipay-web-ui/$module_dir" ]; then
        echo -e "${RED}错误: 模块目录 unipay-web-ui/$module_dir 不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}开始构建 $module_dir 模块...${NC}"
    
    cd "unipay-web-ui/$module_dir" || exit 1
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}错误: 未找到 package.json 文件${NC}"
        cd ../..
        exit 1
    fi
    
    # 检查是否存在npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: 未找到 npm 命令，请确保已安装 Node.js${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${BLUE}安装依赖...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 依赖安装失败${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${BLUE}开始构建...${NC}"
    npm run build
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误: 构建失败${NC}"
        cd ../..
        exit 1
    fi
    
    echo -e "${GREEN}$module_dir 模块构建完成!${NC}"
    cd ../..
}

# 构建所有模块
build_all() {
    echo -e "${GREEN}开始构建所有UI模块...${NC}"
    
    for module_key in "${ALL_MODULES[@]}"; do
        echo -e "${YELLOW}----------------------------------------${NC}"
        build_module "$module_key"
    done
    
    echo -e "${YELLOW}----------------------------------------${NC}"
    echo -e "${GREEN}所有UI模块构建完成!${NC}"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--module)
            MODULE_NAME="$2"
            BUILD_ALL=false
            shift 2
            ;;
        -a|--all)
            BUILD_ALL=true
            shift
            ;;
        -l|--list)
            list_modules
            exit 0
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            usage
            ;;
    esac
done

# 检查是否在项目根目录
if [ ! -d "unipay-web-ui" ]; then
    echo -e "${RED}错误: 未找到 unipay-web-ui 目录，请在项目根目录执行此脚本${NC}"
    exit 1
fi

# 根据参数执行相应操作
if [ "$BUILD_ALL" = true ]; then
    build_all
elif [ -n "$MODULE_NAME" ]; then
    build_module "$MODULE_NAME"
else
    echo -e "${YELLOW}未指定模块，构建所有UI模块...${NC}"
    build_all
fi