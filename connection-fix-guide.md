# UniPay 远程连接超时问题解决方案

## 问题描述
服务器连接远程数据库、MQ和Redis，在长时间空闲后再次请求会出现：
1. Redis连接超时：`RedisCommandTimeoutException: Command timed out after 10 second(s)`
2. MySQL连接失效：`No operations allowed after connection closed`

## 解决方案

### 1. Redis连接优化配置

在所有服务的 `application.yml` 中添加以下Redis配置：

```yaml
spring:
  data:
    redis:
      host: *************
      port: 6379
      timeout: 30000  # 连接超时时间30秒
      database: 1     # 根据服务调整
      password: 
      lettuce:
        pool:
          max-active: 20    # 连接池最大连接数
          max-idle: 10      # 连接池最大空闲连接数
          min-idle: 5       # 连接池最小空闲连接数
          max-wait: 30000   # 连接池最大阻塞等待时间
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30000   # 集群拓扑刷新周期
      # 连接保活配置
      client-type: lettuce
      connect-timeout: 30000
      jedis:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 30000
```

### 2. MySQL连接池优化配置

在所有服务的 `application.yml` 中优化Druid配置：

```yaml
spring:
  datasource:
    url: jdbc:mysql://*************:3306/jeepaydb?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&connectTimeout=60000&socketTimeout=60000
    username: jeepay
    password: 123456
    druid:
      # 连接池配置项
      initial-size: 10              # 初始化连接数
      min-idle: 10                  # 最小空闲连接数
      max-active: 50                # 最大连接数
      max-wait: 60000               # 获取连接最大等待时间
      
      # 连接保活和检测配置
      test-while-idle: true         # 空闲时检测连接有效性
      test-on-borrow: true          # 获取连接时检测
      test-on-return: false         # 归还连接时检测
      validation-query: SELECT 1    # 检测查询语句
      validation-query-timeout: 30  # 检测查询超时时间
      
      # 连接回收配置
      time-between-eviction-runs-millis: 30000    # 检测间隔30秒
      min-evictable-idle-time-millis: 600000      # 连接空闲10分钟后回收
      max-evictable-idle-time-millis: 1800000     # 连接空闲30分钟强制回收
      
      # 连接泄露检测
      remove-abandoned: true        # 开启连接泄露检测
      remove-abandoned-timeout: 1800 # 连接泄露超时时间30分钟
      log-abandoned: true           # 记录连接泄露日志
      
      # 其他配置
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      
      # 连接保活查询
      keep-alive: true
      keep-alive-between-time-millis: 60000  # 保活查询间隔1分钟
```

### 3. ActiveMQ连接优化配置

```yaml
spring:
  activemq:
    broker-url: failover:(tcp://*************:61616?wireFormat.maxInactivityDuration=0&keepAlive=true&maxReconnectDelay=30000&initialReconnectDelay=1000)
    in-memory: false
    user: system
    password: manager
    pool:
      enabled: true
      max-connections: 20           # 增加最大连接数
      idle-timeout: 300000          # 空闲超时5分钟
      expiry-timeout: 0             # 连接过期时间，0表示不过期
      create-connection-on-startup: true
    packages:
      trust-all: true
    close-timeout: 15000
    send-timeout: 30000
```

### 4. 应用级别配置优化

在每个服务的 `application.yml` 中添加：

```yaml
# HTTP连接超时配置
server:
  tomcat:
    connection-timeout: 60000      # Tomcat连接超时
    keep-alive-timeout: 60000      # Keep-Alive超时
    max-keep-alive-requests: 100   # 最大Keep-Alive请求数
  
# 应用配置
spring:
  task:
    execution:
      pool:
        core-size: 10
        max-size: 50
        queue-capacity: 1000
        keep-alive: 60s
    scheduling:
      pool:
        size: 10
```

### 5. JVM参数优化

启动应用时添加以下JVM参数：

```bash
-Dspring.redis.timeout=30000
-Dspring.datasource.druid.keep-alive=true
-Djava.net.preferIPv4Stack=true
-Dnetworkaddress.cache.ttl=60
-Dsun.net.useExclusiveBind=false
```

### 6. 系统级别优化

#### 6.1 TCP Keep-Alive配置（Linux系统）

```bash
# 编辑 /etc/sysctl.conf
echo "net.ipv4.tcp_keepalive_time = 600" >> /etc/sysctl.conf
echo "net.ipv4.tcp_keepalive_intvl = 60" >> /etc/sysctl.conf  
echo "net.ipv4.tcp_keepalive_probes = 3" >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

#### 6.2 防火墙和网络配置

确保防火墙允许长连接：
```bash
# 检查连接状态
netstat -an | grep :3306
netstat -an | grep :6379
netstat -an | grep :61616
```

### 7. 监控和健康检查

#### 7.1 添加健康检查端点

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,druid
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: true
    db:
      enabled: true
```

#### 7.2 添加连接池监控

在启动类中添加：

```java
@Bean
public ServletRegistrationBean<StatViewServlet> druidStatViewServlet() {
    ServletRegistrationBean<StatViewServlet> bean = new ServletRegistrationBean<>(new StatViewServlet(), "/druid/*");
    bean.addInitParameter("allow", "127.0.0.1,***********/24");
    bean.addInitParameter("deny", "");
    bean.addInitParameter("loginUsername", "admin");
    bean.addInitParameter("loginPassword", "admin");
    bean.addInitParameter("resetEnable", "false");
    return bean;
}
```

### 8. 应用代码优化建议

#### 8.1 Redis连接重试机制

```java
@Component
public class RedisConnectionManager {
    
    @Retryable(value = {RedisConnectionFailureException.class}, 
               maxAttempts = 3, 
               backoff = @Backoff(delay = 1000))
    public void executeRedisOperation(Runnable operation) {
        operation.run();
    }
}
```

#### 8.2 数据库连接检查

```java
@Component
public class DatabaseHealthChecker {
    
    @Autowired
    private DataSource dataSource;
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            if (!connection.isValid(10)) {
                log.warn("Database connection is not valid");
            }
        } catch (SQLException e) {
            log.error("Database connection check failed", e);
        }
    }
}
```

### 9. 部署建议

1. **分阶段部署**：先在测试环境验证配置
2. **监控部署**：部署后密切监控连接状态
3. **回滚准备**：准备快速回滚方案
4. **负载均衡**：考虑使用连接池负载均衡

### 10. 故障排查命令

```bash
# 检查Redis连接
redis-cli -h ************* -p 6379 ping

# 检查MySQL连接
mysql -h ************* -P 3306 -u jeepay -p -e "SELECT 1"

# 检查ActiveMQ连接
telnet ************* 61616

# 查看应用连接状态
netstat -an | grep ESTABLISHED | grep -E "(3306|6379|61616)"

# 查看应用日志
tail -f logs/application.log | grep -E "(timeout|connection|redis|mysql)"
```

## 实施步骤

1. **备份现有配置**
2. **更新Redis配置**（优先级最高）
3. **更新数据库连接池配置**
4. **更新ActiveMQ配置**
5. **重启服务并监控**
6. **验证长时间空闲后的连接恢复**

## 预期效果

- Redis连接超时问题解决
- 数据库连接池自动恢复失效连接
- 长时间空闲后服务快速恢复响应
- 提高系统整体稳定性和可用性


# UniPay 通用配置文件修复完成

## ✅ 修复位置
**文件**: `conf/devCommons/config/application.yml`
**影响**: 所有使用通用配置的服务（sys-agent、sys-manager、sys-merchant、sys-payment）

## 🔧 关键修复内容

### 1. Redis连接优化
```yaml
spring:
  data:
    redis:
      host: *************
      port: 6379
      timeout: 30000  # ✅ 从10000ms增加到30000ms
      database: 0
      password: LHX.0618
      lettuce:
        pool:
          max-active: 20    # ✅ 新增连接池配置
          max-idle: 10      
          min-idle: 5       
          max-wait: 30000   
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30000   
      client-type: lettuce
      connect-timeout: 30000  # ✅ 新增连接超时配置
```

### 2. MySQL连接池优化
```yaml
spring:
  datasource:
    # ✅ URL增加连接超时参数
    url: jdbc:mysql://*************:3306/jeepaydb?...&serverTimezone=Asia/Shanghai&connectTimeout=60000&socketTimeout=60000
    username: root
    password: LHX.0618
    druid:
      # ✅ 连接池优化
      initial-size: 10              # 从5增加到10
      min-idle: 10                  # 从5增加到10
      max-active: 50                # 从30增加到50
      
      # ✅ 连接保活和检测
      test-on-borrow: true          # 从false改为true
      validation-query-timeout: 30  # 新增
      
      # ✅ 连接回收优化
      time-between-eviction-runs-millis: 30000    # 从60000减少到30000
      min-evictable-idle-time-millis: 600000      # 从300000增加到600000
      max-evictable-idle-time-millis: 1800000     # 新增
      
      # ✅ 连接泄露检测
      remove-abandoned: true        # 新增
      remove-abandoned-timeout: 1800 # 新增
      log-abandoned: true           # 新增
      
      # ✅ 连接保活
      keep-alive: true              # 新增
      keep-alive-between-time-millis: 60000  # 新增
      
      filters: stat,wall,slf4j      # 增加slf4j
```

### 3. ActiveMQ连接优化
```yaml
spring:
  activemq:
    # ✅ 增加连接保活和重连参数
    broker-url: failover:(tcp://*************:61616?wireFormat.maxInactivityDuration=0&keepAlive=true&maxReconnectDelay=30000&initialReconnectDelay=1000)
    pool:
      enabled: true
      max-connections: 20           # 从10增加到20
      idle-timeout: 300000          # 从30000增加到300000
      expiry-timeout: 0             # 新增
      create-connection-on-startup: true  # 新增
    packages:
      trust-all: true               # 新增
    close-timeout: 15000            # 新增
    send-timeout: 30000             # 新增
```

## 🚀 立即执行步骤

### 1. 重启所有服务
```bash
# 停止所有Java服务
pkill -f "java.*sys-"

# 等待进程完全停止
sleep 5

# 按顺序重启服务
nohup java -jar sys-payment/target/sys-payment.jar > logs/payment.log 2>&1 &
sleep 10
nohup java -jar sys-manager/target/sys-manager.jar > logs/manager.log 2>&1 &
sleep 10  
nohup java -jar sys-merchant/target/sys-merchant.jar > logs/merchant.log 2>&1 &
sleep 10
nohup java -jar sys-agent/target/sys-agent.jar > logs/agent.log 2>&1 &
```

### 2. 验证服务启动
```bash
# 检查所有服务进程
ps aux | grep java | grep sys-

# 检查端口监听
netstat -tlnp | grep -E "(8080|9216|9218|9219)"

# 检查启动日志
tail -f logs/agent.log | grep -E "(Started|redis|mysql|connection)"
```

### 3. 测试连接恢复
1. **立即测试**: 访问前端页面，确认基本功能正常
2. **空闲测试**: 等待15-20分钟让服务进入空闲状态
3. **恢复测试**: 再次访问前端，观察是否还有连接超时错误

## 📊 监控命令

### 实时错误监控
```bash
# 监控所有服务的连接错误
tail -f logs/*.log | grep -E "(timeout|connection.*closed|redis.*timeout|mysql.*closed)"

# 监控连接数变化
watch -n 5 "netstat -an | grep ESTABLISHED | grep -E '(3306|6379|61616)' | wc -l"
```

### 连接状态检查
```bash
# 测试Redis连接
redis-cli -h ************* -p 6379 -a LHX.0618 ping

# 测试MySQL连接  
mysql -h ************* -P 3306 -u root -pLHX.0618 -e "SELECT 1"

# 测试ActiveMQ连接
telnet ************* 61616
```

## 🎯 预期效果

修复后应该彻底解决：
- ✅ **Redis连接超时**: `RedisCommandTimeoutException: Command timed out after 10 second(s)`
- ✅ **MySQL连接失效**: `No operations allowed after connection closed`
- ✅ **长时间空闲后连接恢复慢**的问题
- ✅ **HikariPool连接验证失败**的警告

## ⚠️ 重要说明

1. **配置生效**: 由于修改的是通用配置文件，重启后所有服务都会使用新配置
2. **数据库权限**: 确认root用户有足够权限进行连接保活查询
3. **网络稳定性**: 如果网络环境特别不稳定，可以进一步增加超时时间
4. **监控重要性**: 重启后请密切监控日志，确认没有新的连接错误

## 🔍 故障排查

如果问题仍然存在：

1. **检查配置加载**:
```bash
# 确认服务使用的配置文件
grep -r "devCommons" sys-*/pom.xml
```

2. **检查连接参数**:
```bash
# 查看实际生效的连接配置
curl http://localhost:9219/actuator/configprops | grep -A 20 redis
```

3. **增加调试日志**:
```yaml
logging:
  level:
    com.zaxxer.hikari: DEBUG
    io.lettuce.core: DEBUG
```

## 📞 技术支持

如果按照以上步骤操作后问题仍然存在，请提供：
1. 重启后的完整启动日志
2. `netstat -an | grep ESTABLISHED` 输出
3. 具体的错误发生时间和操作步骤
4. Redis/MySQL服务器的状态信息

---
**修复完成时间**: 2025-09-12 21:00
**修复范围**: 通用配置文件，影响所有服务
**预计生效时间**: 重启服务后立即生效